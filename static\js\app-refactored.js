/**
 * AutoPilot AI - 重构版前端应用
 * 
 * 支持两阶段交互模式和TTS播报的现代化界面
 */

class TravelPlannerAppRefactored {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.currentPhase = 'waiting'; // waiting, analysis, planning, completed
        
        // 分析步骤状态
        this.analysisSteps = {
            'user_intent': { completed: false, title: '解析用户需求和画像' },
            'poi_preference': { completed: false, title: '景点偏好类型' },
            'food_preference': { completed: false, title: '美食偏好' },
            'accommodation_preference': { completed: false, title: '住宿偏好' }
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
        this.updateUI();
    }
    
    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });
        
        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });
        
        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });
        
        // 立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startItineraryPlanning();
            });
        }
        
        // 取消规划按钮
        const cancelPlanningBtn = document.getElementById('cancelPlanningBtn');
        if (cancelPlanningBtn) {
            cancelPlanningBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });
        
        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });
        
        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });
        
        // 历史行程按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }
    
    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }
    
    updateUI() {
        // 根据当前阶段更新UI显示
        this.hideAllViews();
        
        switch (this.currentPhase) {
            case 'waiting':
                this.showWaitingView();
                break;
            case 'analysis':
                this.showAnalysisView();
                break;
            case 'planning':
                this.showPlanningView();
                break;
            case 'completed':
                this.showCompletedView();
                break;
        }
    }
    
    hideAllViews() {
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';
        document.getElementById('itineraryView').style.display = 'none';
    }
    
    showWaitingView() {
        document.getElementById('waitingView').style.display = 'flex';
    }
    
    showAnalysisView() {
        document.getElementById('analysisView').style.display = 'flex';
        
        // 更新分析状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');
        
        if (title) title.textContent = '正在分析您的需求...';
        if (desc) desc.textContent = 'AI正在理解您的旅行偏好和需求';
    }
    
    showPlanningView() {
        document.getElementById('analysisView').style.display = 'flex';
        
        // 更新规划状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');
        
        if (title) title.textContent = '正在生成旅行方案...';
        if (desc) desc.textContent = 'AI正在为您规划详细的行程安排';
    }
    
    showCompletedView() {
        document.getElementById('itineraryView').style.display = 'block';
    }
    
    async startPlanning() {
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim();
        
        if (!query) {
            this.showAlert('请输入您的旅行想法', 'warning');
            return;
        }
        
        try {
            // 切换到分析阶段
            this.currentPhase = 'analysis';
            this.updateUI();
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始分析您的旅行需求');
            }
            
            // 重置分析步骤状态
            this.resetAnalysisSteps();
            
            // 模拟分析过程
            await this.simulateAnalysisProcess(query);
            
            // 显示立即规划按钮
            this.showStartPlanningButton();
            
        } catch (error) {
            console.error('规划失败:', error);
            this.showAlert('规划失败: ' + error.message, 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        }
    }
    
    resetAnalysisSteps() {
        Object.keys(this.analysisSteps).forEach(stepKey => {
            this.analysisSteps[stepKey].completed = false;
            const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
            if (stepElement) {
                stepElement.classList.remove('completed');
                stepElement.classList.remove('active');
                
                // 重置结果显示
                const resultElement = stepElement.querySelector('.analysis-result');
                if (resultElement) {
                    resultElement.innerHTML = '<div class="loading-placeholder">等待分析...</div>';
                }
                
                // 显示加载状态
                const statusElement = stepElement.querySelector('.analysis-status');
                if (statusElement) {
                    statusElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                }
            }
        });
    }
    
    async simulateAnalysisProcess(query) {
        const steps = [
            {
                key: 'user_intent',
                title: '解析用户需求和画像',
                content: '北京2天 | 历史文化 | 深度游 | 摄影爱好者',
                delay: 2000
            },
            {
                key: 'poi_preference',
                title: '景点偏好类型',
                content: '历史文化景点 | 古建筑 | 博物馆 | 适合摄影',
                delay: 1500
            },
            {
                key: 'food_preference',
                title: '美食偏好',
                content: '地方特色 | 老字号 | 不辣 | 口碑餐厅',
                delay: 1500
            },
            {
                key: 'accommodation_preference',
                title: '住宿偏好',
                content: '市区便利 | 中等价位 | 交通便利 | 连锁酒店',
                delay: 1500
            }
        ];
        
        for (const step of steps) {
            // 设置当前步骤为活跃状态
            this.setAnalysisStepActive(step.key);
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakAnalysisStep(step.title, '正在分析');
            }
            
            // 等待
            await new Promise(resolve => setTimeout(resolve, step.delay));
            
            // 完成当前步骤
            this.completeAnalysisStep(step.key, step.content);
            
            // TTS播报结果
            if (window.ttsManager) {
                window.ttsManager.speakAnalysisStep(step.title, step.content);
            }
        }
    }
    
    setAnalysisStepActive(stepKey) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        }
    }
    
    completeAnalysisStep(stepKey, content) {
        this.analysisSteps[stepKey].completed = true;
        
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // 更新结果显示
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = `<div class="analysis-content-text">${content}</div>`;
            }
            
            // 更新状态图标
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
            }
        }
    }
    
    showStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        const cancelBtn = document.getElementById('cancelPlanningBtn');
        
        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.classList.add('animate__animated', 'animate__fadeInUp');
        }
        
        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }
        
        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('分析完成，点击立即规划开始生成行程');
        }
    }
    
    async startItineraryPlanning() {
        try {
            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();
            
            // 隐藏按钮
            document.getElementById('startPlanningBtn').style.display = 'none';
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }
            
            // 模拟行程生成过程
            await this.simulateItineraryGeneration();
            
        } catch (error) {
            console.error('行程规划失败:', error);
            this.showAlert('行程规划失败: ' + error.message, 'danger');
        }
    }
    
    async simulateItineraryGeneration() {
        // 等待3秒模拟生成过程
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 模拟行程数据
        const mockItinerary = {
            title: '北京2天历史文化深度游',
            description: '专为摄影爱好者定制的历史文化之旅',
            days: 2,
            totalPOIs: 6,
            estimatedBudget: '¥1,500',
            weather: '晴'
        };
        
        // 显示行程
        this.displayItinerary(mockItinerary);
        
        // 切换到完成阶段
        this.currentPhase = 'completed';
        this.updateUI();
        
        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakItineraryInfo(mockItinerary.title, mockItinerary.description);
        }
    }
    
    displayItinerary(itinerary) {
        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = itinerary.title;
        document.getElementById('itineraryDescription').textContent = itinerary.description;
        
        // 更新统计信息
        document.getElementById('totalDays').textContent = itinerary.days;
        document.getElementById('totalPOIs').textContent = itinerary.totalPOIs;
        document.getElementById('estimatedBudget').textContent = itinerary.estimatedBudget;
        document.getElementById('weatherInfo').textContent = itinerary.weather;
        
        this.currentItinerary = itinerary;
    }
    
    cancelPlanning() {
        // 停止当前规划
        this.currentPhase = 'waiting';
        this.updateUI();
        
        // 重置表单
        document.getElementById('userQuery').value = '';
        
        // 隐藏按钮
        document.getElementById('startPlanningBtn').style.display = 'none';
        document.getElementById('cancelPlanningBtn').style.display = 'none';
        
        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('规划已取消');
        }
        
        this.showAlert('规划已取消', 'info');
    }
    
    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const itineraryView = document.getElementById('itineraryView');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'block';
            if (mapView) mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'none';
            if (mapView) mapView.style.display = 'block';
        }
    }
    
    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('行程已保存');
            }
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }
    
    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }
    
    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.title,
                text: this.currentItinerary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }
    
    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }
    
    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TravelPlannerAppRefactored();
});
