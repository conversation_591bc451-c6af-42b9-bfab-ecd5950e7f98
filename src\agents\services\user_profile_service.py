"""
用户画像服务 (User Profile Service)

提供用户画像管理的原子化服务，包括画像获取、更新、分析等功能。
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from src.models.travel_planner import UserProfile
from src.database.mysql_client import MySQL<PERSON>lient
from src.database.mongodb_client import MongoDBClient

logger = logging.getLogger(__name__)


class UserProfileService:
    """用户画像服务类"""
    
    def __init__(self):
        """初始化用户画像服务"""
        self.mysql_client = MySQLClient()
        self.mongodb_client = MongoDBClient()
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户画像
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户画像信息，如果不存在则返回None
        """
        try:
            logger.info(f"获取用户画像 - 用户ID: {user_id}")
            
            # 从MySQL获取基础用户信息
            query = """
            SELECT user_id, age_group, travel_style, budget_level, 
                   interests, dietary_restrictions, accessibility_needs,
                   preferred_languages, created_at, updated_at
            FROM user_profiles 
            WHERE user_id = %s AND is_active = 1
            """
            
            result = await self.mysql_client.fetch_one(query, (user_id,))
            
            if not result:
                logger.info(f"用户画像不存在 - 用户ID: {user_id}")
                return None
            
            # 解析JSON字段
            profile = {
                "user_id": result["user_id"],
                "age_group": result["age_group"],
                "travel_style": result["travel_style"],
                "budget_level": result["budget_level"],
                "interests": json.loads(result["interests"]) if result["interests"] else [],
                "dietary_restrictions": json.loads(result["dietary_restrictions"]) if result["dietary_restrictions"] else [],
                "accessibility_needs": json.loads(result["accessibility_needs"]) if result["accessibility_needs"] else [],
                "preferred_languages": json.loads(result["preferred_languages"]) if result["preferred_languages"] else [],
                "created_at": result["created_at"].isoformat() if result["created_at"] else None,
                "updated_at": result["updated_at"].isoformat() if result["updated_at"] else None
            }
            
            # 从MongoDB获取扩展画像信息
            extended_profile = await self.mongodb_client.find_one(
                "user_profiles_extended",
                {"user_id": user_id}
            )
            
            if extended_profile:
                profile.update({
                    "travel_history": extended_profile.get("travel_history", []),
                    "preference_scores": extended_profile.get("preference_scores", {}),
                    "behavioral_patterns": extended_profile.get("behavioral_patterns", {}),
                    "social_preferences": extended_profile.get("social_preferences", {}),
                    "seasonal_preferences": extended_profile.get("seasonal_preferences", {})
                })
            
            logger.info(f"用户画像获取成功 - 用户ID: {user_id}")
            return profile
            
        except Exception as e:
            logger.error(f"获取用户画像失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    async def create_user_profile(
        self,
        user_id: str,
        profile_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建用户画像
        
        Args:
            user_id: 用户ID
            profile_data: 画像数据
            
        Returns:
            创建的用户画像
        """
        try:
            logger.info(f"创建用户画像 - 用户ID: {user_id}")
            
            # 准备基础画像数据
            basic_data = {
                "user_id": user_id,
                "age_group": profile_data.get("age_group"),
                "travel_style": profile_data.get("travel_style"),
                "budget_level": profile_data.get("budget_level"),
                "interests": json.dumps(profile_data.get("interests", []), ensure_ascii=False),
                "dietary_restrictions": json.dumps(profile_data.get("dietary_restrictions", []), ensure_ascii=False),
                "accessibility_needs": json.dumps(profile_data.get("accessibility_needs", []), ensure_ascii=False),
                "preferred_languages": json.dumps(profile_data.get("preferred_languages", []), ensure_ascii=False),
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "is_active": 1
            }
            
            # 插入到MySQL
            insert_query = """
            INSERT INTO user_profiles 
            (user_id, age_group, travel_style, budget_level, interests, 
             dietary_restrictions, accessibility_needs, preferred_languages, 
             created_at, updated_at, is_active)
            VALUES (%(user_id)s, %(age_group)s, %(travel_style)s, %(budget_level)s, 
                    %(interests)s, %(dietary_restrictions)s, %(accessibility_needs)s, 
                    %(preferred_languages)s, %(created_at)s, %(updated_at)s, %(is_active)s)
            """
            
            await self.mysql_client.execute(insert_query, basic_data)
            
            # 准备扩展画像数据
            extended_data = {
                "user_id": user_id,
                "travel_history": profile_data.get("travel_history", []),
                "preference_scores": profile_data.get("preference_scores", {}),
                "behavioral_patterns": profile_data.get("behavioral_patterns", {}),
                "social_preferences": profile_data.get("social_preferences", {}),
                "seasonal_preferences": profile_data.get("seasonal_preferences", {}),
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            # 插入到MongoDB
            await self.mongodb_client.insert_one("user_profiles_extended", extended_data)
            
            logger.info(f"用户画像创建成功 - 用户ID: {user_id}")
            
            # 返回完整画像
            return await self.get_user_profile(user_id)
            
        except Exception as e:
            logger.error(f"创建用户画像失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    async def update_user_profile(
        self,
        user_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新用户画像
        
        Args:
            user_id: 用户ID
            update_data: 更新数据
            
        Returns:
            更新后的用户画像
        """
        try:
            logger.info(f"更新用户画像 - 用户ID: {user_id}")
            
            # 准备基础字段更新
            basic_fields = ["age_group", "travel_style", "budget_level", 
                          "interests", "dietary_restrictions", "accessibility_needs", "preferred_languages"]
            
            basic_updates = {}
            for field in basic_fields:
                if field in update_data:
                    if field in ["interests", "dietary_restrictions", "accessibility_needs", "preferred_languages"]:
                        basic_updates[field] = json.dumps(update_data[field], ensure_ascii=False)
                    else:
                        basic_updates[field] = update_data[field]
            
            if basic_updates:
                basic_updates["updated_at"] = datetime.now()
                
                # 构建更新查询
                set_clause = ", ".join([f"{k} = %({k})s" for k in basic_updates.keys()])
                update_query = f"UPDATE user_profiles SET {set_clause} WHERE user_id = %(user_id)s"
                basic_updates["user_id"] = user_id
                
                await self.mysql_client.execute(update_query, basic_updates)
            
            # 准备扩展字段更新
            extended_fields = ["travel_history", "preference_scores", "behavioral_patterns", 
                             "social_preferences", "seasonal_preferences"]
            
            extended_updates = {}
            for field in extended_fields:
                if field in update_data:
                    extended_updates[field] = update_data[field]
            
            if extended_updates:
                extended_updates["updated_at"] = datetime.now()
                
                await self.mongodb_client.update_one(
                    "user_profiles_extended",
                    {"user_id": user_id},
                    {"$set": extended_updates}
                )
            
            logger.info(f"用户画像更新成功 - 用户ID: {user_id}")
            
            # 返回更新后的画像
            return await self.get_user_profile(user_id)
            
        except Exception as e:
            logger.error(f"更新用户画像失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    async def analyze_user_preferences(
        self,
        user_id: str,
        travel_history: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        分析用户偏好
        
        Args:
            user_id: 用户ID
            travel_history: 旅行历史（可选）
            
        Returns:
            偏好分析结果
        """
        try:
            logger.info(f"分析用户偏好 - 用户ID: {user_id}")
            
            # 获取用户画像
            profile = await self.get_user_profile(user_id)
            if not profile:
                return {}
            
            # 获取旅行历史
            if not travel_history:
                travel_history = profile.get("travel_history", [])
            
            # 分析偏好模式
            preference_analysis = {
                "destination_preferences": self._analyze_destination_preferences(travel_history),
                "activity_preferences": self._analyze_activity_preferences(travel_history),
                "budget_patterns": self._analyze_budget_patterns(travel_history),
                "seasonal_patterns": self._analyze_seasonal_patterns(travel_history),
                "travel_style_evolution": self._analyze_travel_style_evolution(travel_history)
            }
            
            logger.info(f"用户偏好分析完成 - 用户ID: {user_id}")
            return preference_analysis
            
        except Exception as e:
            logger.error(f"用户偏好分析失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    def _analyze_destination_preferences(self, travel_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析目的地偏好"""
        destinations = {}
        for trip in travel_history:
            for dest in trip.get("destinations", []):
                destinations[dest] = destinations.get(dest, 0) + 1
        
        return {
            "frequent_destinations": sorted(destinations.items(), key=lambda x: x[1], reverse=True)[:5],
            "destination_diversity": len(destinations),
            "repeat_visit_rate": sum(1 for count in destinations.values() if count > 1) / len(destinations) if destinations else 0
        }
    
    def _analyze_activity_preferences(self, travel_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析活动偏好"""
        activities = {}
        for trip in travel_history:
            for activity in trip.get("activities", []):
                activities[activity] = activities.get(activity, 0) + 1
        
        return {
            "preferred_activities": sorted(activities.items(), key=lambda x: x[1], reverse=True)[:10],
            "activity_diversity": len(activities)
        }
    
    def _analyze_budget_patterns(self, travel_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析预算模式"""
        budgets = [trip.get("budget", 0) for trip in travel_history if trip.get("budget")]
        
        if not budgets:
            return {}
        
        return {
            "average_budget": sum(budgets) / len(budgets),
            "budget_range": {"min": min(budgets), "max": max(budgets)},
            "budget_trend": "increasing" if len(budgets) > 1 and budgets[-1] > budgets[0] else "stable"
        }
    
    def _analyze_seasonal_patterns(self, travel_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析季节偏好"""
        seasons = {}
        for trip in travel_history:
            season = trip.get("season")
            if season:
                seasons[season] = seasons.get(season, 0) + 1
        
        return {
            "preferred_seasons": sorted(seasons.items(), key=lambda x: x[1], reverse=True),
            "seasonal_diversity": len(seasons)
        }
    
    def _analyze_travel_style_evolution(self, travel_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析旅行风格演变"""
        styles = [trip.get("style") for trip in travel_history if trip.get("style")]
        
        return {
            "style_progression": styles,
            "current_style": styles[-1] if styles else None,
            "style_consistency": len(set(styles)) == 1 if styles else False
        }
