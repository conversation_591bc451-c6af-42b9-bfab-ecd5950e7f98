"""
TravelPlannerAgent LangGraph节点函数

实现了旅行规划Agent的所有节点函数，支持双模运行和状态管理。
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .state import TravelPlanState, ProcessingStage, PlanningMode, add_event_to_state, update_state_stage
from ..services.analysis_service import AnalysisService
from ..services.user_profile_service import UserProfileService
from ..services.amap_service import AmapService
from ..services.reasoning_service import ReasoningService
from ..services.memory_service import MemoryService
from src.prompts import apply_prompt_template

logger = logging.getLogger(__name__)


async def core_intent_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    核心意图分析节点
    
    分析用户的原始查询，提取核心旅行意图和关键信息。
    """
    logger.info(f"开始核心意图分析 - Session: {state['session_id']}")
    
    try:
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "intent_analysis",
            "message": "开始分析您的旅行意图..."
        })
        
        # 获取用户历史记忆
        memory_service = MemoryService()
        user_memories = await memory_service.get_user_memories(state["user_id"])
        state["user_memories"] = user_memories
        
        # 构建提示词变量
        template_vars = {
            "original_query": state["original_query"],
            "user_profile": state.get("user_profile"),
            "user_memories": user_memories
        }
        
        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01_core_intent_analyzer",
            template_vars
        )
        
        # 调用推理服务
        reasoning_service = ReasoningService()
        response = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="core_intent_schema"
        )
        
        # 解析结果
        core_intent = json.loads(response.content)
        state["core_intent"] = core_intent
        
        # 记录工具调用
        state["tool_calls"].append({
            "node": "core_intent_analyzer",
            "timestamp": datetime.now().isoformat(),
            "input": template_vars,
            "output": core_intent
        })
        
        # 添加完成事件
        state = add_event_to_state(state, "intent_analyzed", {
            "destinations": core_intent.get("destinations", []),
            "days": core_intent.get("days"),
            "travel_theme": core_intent.get("travel_theme"),
            "confidence_score": core_intent.get("confidence_score", 0)
        })
        
        logger.info(f"核心意图分析完成 - 目的地: {core_intent.get('destinations')}")
        
    except Exception as e:
        logger.error(f"核心意图分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"意图分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "intent_analysis",
            "error": str(e)
        })
    
    return state


async def multi_city_strategy_node(state: TravelPlanState) -> TravelPlanState:
    """
    多城市策略分析节点
    
    当涉及多个目的地时，制定最优的路线策略和时间分配。
    """
    logger.info(f"开始多城市策略分析 - Session: {state['session_id']}")
    
    try:
        core_intent = state.get("core_intent", {})
        destinations = core_intent.get("destinations", [])
        
        # 如果只有一个目的地，跳过此节点
        if len(destinations) <= 1:
            logger.info("单一目的地，跳过多城市策略分析")
            return state
        
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "multi_city_strategy",
            "message": f"正在为您的{len(destinations)}个目的地制定最优路线..."
        })
        
        # 获取距离矩阵
        amap_service = AmapService()
        distance_matrix = await amap_service.get_distance_matrix(destinations)
        
        # 构建提示词变量
        template_vars = {
            "destinations": destinations,
            "total_days": core_intent.get("days"),
            "distance_matrix": distance_matrix,
            "user_preferences": core_intent.get("preferences"),
            "transportation_mode": core_intent.get("transportation", {}).get("primary_mode")
        }
        
        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01a_multi_city_strategy_analyzer",
            template_vars
        )
        
        # 调用推理服务
        reasoning_service = ReasoningService()
        response = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="multi_city_strategy_schema"
        )
        
        # 解析结果
        multi_city_strategy = json.loads(response.content)
        state["multi_city_strategy"] = multi_city_strategy
        
        # 记录API调用
        state["api_calls"].append({
            "service": "amap_distance_matrix",
            "timestamp": datetime.now().isoformat(),
            "input": destinations,
            "output": distance_matrix
        })
        
        # 添加完成事件
        state = add_event_to_state(state, "strategy_planned", {
            "strategy_type": multi_city_strategy.get("strategy_type"),
            "recommended_order": multi_city_strategy.get("recommended_order"),
            "total_travel_time": multi_city_strategy.get("total_travel_time")
        })
        
        logger.info(f"多城市策略分析完成 - 策略: {multi_city_strategy.get('strategy_type')}")
        
    except Exception as e:
        logger.error(f"多城市策略分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"多城市策略分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "multi_city_strategy",
            "error": str(e)
        })
    
    return state


async def driving_context_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    驾驶情境分析节点
    
    分析用户的车辆信息和驾驶需求，确定规划模式。
    """
    logger.info(f"开始驾驶情境分析 - Session: {state['session_id']}")
    
    try:
        core_intent = state.get("core_intent", {})
        transportation = core_intent.get("transportation", {})
        
        # 检查是否为自驾出行
        if transportation.get("primary_mode") != "self_driving":
            logger.info("非自驾出行，跳过驾驶情境分析")
            return state
        
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "driving_context",
            "message": "正在分析您的驾驶需求和车辆信息..."
        })
        
        # 构建提示词变量
        template_vars = {
            "user_vehicle_info": state.get("vehicle_info"),
            "destinations": core_intent.get("destinations"),
            "total_days": core_intent.get("days"),
            "user_preferences": core_intent.get("preferences")
        }
        
        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01b_driving_context_analyzer",
            template_vars
        )
        
        # 调用推理服务
        reasoning_service = ReasoningService()
        response = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="driving_context_schema"
        )
        
        # 解析结果
        driving_context = json.loads(response.content)
        state["driving_context"] = driving_context
        
        # 根据分析结果设置规划模式
        driving_strategy = driving_context.get("driving_strategy")
        if driving_strategy == "range_aware":
            state["planning_mode"] = PlanningMode.RANGE_AWARE.value
        else:
            state["planning_mode"] = PlanningMode.GENERAL_ASSISTANCE.value
        
        # 添加完成事件
        state = add_event_to_state(state, "driving_analyzed", {
            "driving_strategy": driving_strategy,
            "planning_mode": state["planning_mode"],
            "vehicle_type": driving_context.get("vehicle_analysis", {}).get("vehicle_type")
        })
        
        logger.info(f"驾驶情境分析完成 - 策略: {driving_strategy}")
        
    except Exception as e:
        logger.error(f"驾驶情境分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"驾驶情境分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "driving_context",
            "error": str(e)
        })
    
    return state


async def preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    偏好分析节点
    
    分析用户的景点、美食等偏好，构建偏好画像。
    """
    logger.info(f"开始偏好分析 - Session: {state['session_id']}")
    
    try:
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "preference_analysis",
            "message": "正在分析您的旅行偏好..."
        })
        
        core_intent = state.get("core_intent", {})
        
        # 分析景点偏好
        attraction_template_vars = {
            "core_intent": core_intent,
            "user_profile": state.get("user_profile"),
            "user_memories": state.get("user_memories"),
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }
        
        attraction_messages = apply_prompt_template(
            "travel_planner/02_attraction_preference_analyzer",
            attraction_template_vars
        )
        
        # 分析美食偏好
        food_template_vars = {
            "core_intent": core_intent,
            "user_profile": state.get("user_profile"),
            "user_memories": state.get("user_memories"),
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days"),
            "travelers": core_intent.get("travelers")
        }
        
        food_messages = apply_prompt_template(
            "travel_planner/03_food_preference_analyzer",
            food_template_vars
        )
        
        # 调用推理服务
        reasoning_service = ReasoningService()
        
        # 并行分析景点和美食偏好
        attraction_response = await reasoning_service.analyze_with_structured_output(
            messages=attraction_messages,
            response_format="attraction_preference_schema"
        )
        
        food_response = await reasoning_service.analyze_with_structured_output(
            messages=food_messages,
            response_format="food_preference_schema"
        )
        
        # 解析结果
        attraction_preferences = json.loads(attraction_response.content)
        food_preferences = json.loads(food_response.content)
        
        # 构建偏好画像
        preference_profile = {
            "attraction_preferences": attraction_preferences,
            "food_preferences": food_preferences,
            "confidence_score": (
                attraction_preferences.get("confidence_score", 0) +
                food_preferences.get("confidence_score", 0)
            ) / 2
        }
        
        state["preference_profile"] = preference_profile
        
        # 添加完成事件
        state = add_event_to_state(state, "preferences_analyzed", {
            "attraction_confidence": attraction_preferences.get("confidence_score", 0),
            "food_confidence": food_preferences.get("confidence_score", 0),
            "overall_confidence": preference_profile["confidence_score"]
        })
        
        logger.info("偏好分析完成")
        
    except Exception as e:
        logger.error(f"偏好分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"偏好分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "preference_analysis",
            "error": str(e)
        })
    
    return state


def should_analyze_multi_city(state: TravelPlanState) -> str:
    """判断是否需要多城市策略分析"""
    core_intent = state.get("core_intent", {})
    destinations = core_intent.get("destinations", [])
    
    if len(destinations) > 1:
        return "multi_city_strategy"
    else:
        return "driving_context"


def should_analyze_driving_context(state: TravelPlanState) -> str:
    """判断是否需要驾驶情境分析"""
    core_intent = state.get("core_intent", {})
    transportation = core_intent.get("transportation", {})
    
    if transportation.get("primary_mode") == "self_driving":
        return "driving_context"
    else:
        return "preference_analysis"


def has_error(state: TravelPlanState) -> str:
    """检查是否有错误"""
    if state.get("has_error", False):
        return "error_handler"
    else:
        return "continue"
