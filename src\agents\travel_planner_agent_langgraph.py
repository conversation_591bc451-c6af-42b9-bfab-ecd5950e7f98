"""
TravelPlannerAgent LangGraph实现

基于LangGraph重构的旅行规划Agent，替换原有的autogen实现。
专注于全自动模式实现，支持：
- 完全自动化的旅行规划流程
- 双模运行（精准续航规划 vs 通用驾驶辅助）
- 状态管理和SSE事件流
- 交互模式预留钩子
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

from .travel_planner_langgraph import TravelPlannerGraph, create_initial_state, UserProfile, VehicleInfo
from .travel_planner_langgraph.stream_adapter import SSEStreamAdapter
from .services import UserProfileService, MemoryService

logger = logging.getLogger(__name__)


class TravelPlannerAgentLangGraph:
    """
    基于LangGraph的旅行规划Agent

    专注于全自动模式实现，提供完整的旅行规划服务：
    - 全自动规划：无需用户交互，直接生成完整方案
    - 流式规划：实时进度更新，适合前端展示
    - 双模运行：支持精准续航规划和通用驾驶辅助
    - 交互预留：为未来交互模式预留接口
    """

    def __init__(self, enable_interaction_hooks: bool = False):
        """
        初始化Agent

        Args:
            enable_interaction_hooks: 是否启用交互模式钩子（预留功能）
        """
        self.graph = TravelPlannerGraph(enable_interaction_hooks=enable_interaction_hooks)
        self.stream_adapter = SSEStreamAdapter()
        self.user_profile_service = UserProfileService()
        self.memory_service = MemoryService()
        self.enable_interaction_hooks = enable_interaction_hooks
    
    async def plan_travel_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        全自动规划旅行（非流式）

        这是主要的规划方法，完全自动化处理用户请求，
        无需任何用户交互，直接生成完整的旅行方案。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Returns:
            完整的规划结果
        """
        try:
            logger.info(f"开始旅行规划 - 用户: {user_id}")
            
            start_time = datetime.now()
            
            # 获取用户画像（如果未提供）
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)
            
            # 运行全自动工作流
            final_state = await self.graph.run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            final_state["processing_time_seconds"] = processing_time
            
            # 保存用户记忆
            await self._save_planning_memory(user_id, query, final_state)
            
            # 转换为API响应格式
            response = self.stream_adapter.convert_final_state_to_response(final_state)
            
            logger.info(f"旅行规划完成 - 用户: {user_id}, 耗时: {processing_time:.2f}秒")
            return response
            
        except Exception as e:
            logger.error(f"旅行规划失败 - 用户: {user_id}, 错误: {str(e)}")
            raise
    
    async def plan_travel_stream_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        全自动流式规划旅行

        提供实时的规划进度更新，完全自动化处理，
        适合前端SSE事件流展示。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Yields:
            SSE格式的事件流
        """
        try:
            logger.info(f"开始流式旅行规划 - 用户: {user_id}")
            
            # 生成会话ID
            if not session_id:
                session_id = f"travel_plan_{user_id}_{int(datetime.now().timestamp())}"
            
            # 获取用户画像（如果未提供）
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)
            
            # 获取LangGraph全自动流
            langgraph_stream = self.graph.stream_run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id
            )
            
            # 转换为SSE流
            final_state = None
            async for sse_event in self.stream_adapter.convert_langgraph_stream_to_sse(
                langgraph_stream, session_id
            ):
                yield sse_event
                
                # 尝试从事件中提取最终状态（用于保存记忆）
                if "planning_completed" in sse_event or "planning_failed" in sse_event:
                    # 获取最终状态用于保存记忆
                    try:
                        state_history = await self.graph.get_state_history(session_id)
                        if state_history:
                            final_state = state_history[0]  # 最新状态
                    except Exception as e:
                        logger.warning(f"获取最终状态失败: {str(e)}")
            
            # 保存用户记忆
            if final_state:
                await self._save_planning_memory(user_id, query, final_state)
            
            logger.info(f"流式旅行规划完成 - 用户: {user_id}")

        except Exception as e:
            logger.error(f"流式旅行规划失败 - 用户: {user_id}, 错误: {str(e)}")

            # 发送错误事件
            error_event = self.stream_adapter._format_sse_event(
                event_type="error",
                data={
                    "session_id": session_id or "unknown",
                    "user_id": user_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )
            yield error_event

    # ==================== 向后兼容方法 ====================

    async def plan_travel(self, *args, **kwargs) -> Dict[str, Any]:
        """规划旅行（默认全自动模式，保持向后兼容）"""
        return await self.plan_travel_automatic(*args, **kwargs)

    async def plan_travel_stream(self, *args, **kwargs) -> AsyncGenerator[str, None]:
        """流式规划旅行（默认全自动模式，保持向后兼容）"""
        async for event in self.plan_travel_stream_automatic(*args, **kwargs):
            yield event

    # ==================== 交互模式预留方法 ====================

    async def plan_travel_interactive(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        交互式规划旅行（预留功能）

        支持在关键决策点与用户交互，收集反馈并调整规划。
        目前为预留接口，可在后续版本中实现。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）
            interaction_config: 交互配置（可选）

        Returns:
            交互式规划结果
        """
        logger.info(f"交互式规划模式（预留功能） - 用户: {user_id}")

        # 目前回退到全自动模式
        logger.warning("交互式模式尚未实现，回退到全自动模式")
        return await self.plan_travel_automatic(
            user_id=user_id,
            query=query,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            session_id=session_id
        )

    async def plan_travel_stream_interactive(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        交互式流式规划旅行（预留功能）

        支持实时交互和反馈收集的流式规划。
        目前为预留接口，可在后续版本中实现。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）
            interaction_config: 交互配置（可选）

        Yields:
            交互式SSE事件流
        """
        logger.info(f"交互式流式规划模式（预留功能） - 用户: {user_id}")

        # 目前回退到全自动流式模式
        logger.warning("交互式流式模式尚未实现，回退到全自动流式模式")
        async for event in self.plan_travel_stream_automatic(
            user_id=user_id,
            query=query,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            session_id=session_id
        ):
            yield event
    
    async def get_planning_status(self, session_id: str) -> Dict[str, Any]:
        """
        获取规划状态
        
        Args:
            session_id: 会话ID
            
        Returns:
            规划状态信息
        """
        try:
            logger.info(f"获取规划状态 - Session: {session_id}")
            
            # 获取状态历史
            state_history = await self.graph.get_state_history(session_id)
            
            if not state_history:
                return {
                    "session_id": session_id,
                    "status": "not_found",
                    "message": "未找到对应的规划会话"
                }
            
            # 获取最新状态
            latest_state = state_history[0]
            
            # 转换为状态响应
            response = {
                "session_id": session_id,
                "status": "completed" if latest_state.get("is_completed") else "processing",
                "current_stage": latest_state.get("current_stage"),
                "has_error": latest_state.get("has_error", False),
                "error_message": latest_state.get("error_message"),
                "progress": self._calculate_progress(latest_state.get("current_stage")),
                "created_at": latest_state.get("created_at"),
                "updated_at": latest_state.get("updated_at")
            }
            
            # 如果已完成，添加结果
            if latest_state.get("is_completed") and not latest_state.get("has_error"):
                response["result"] = self.stream_adapter.convert_final_state_to_response(latest_state)
            
            return response
            
        except Exception as e:
            logger.error(f"获取规划状态失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    async def cancel_planning(self, session_id: str) -> Dict[str, Any]:
        """
        取消规划
        
        Args:
            session_id: 会话ID
            
        Returns:
            取消结果
        """
        try:
            logger.info(f"取消规划 - Session: {session_id}")
            
            # 这里可以实现取消逻辑
            # LangGraph目前没有直接的取消机制，可以通过状态标记实现
            
            return {
                "session_id": session_id,
                "status": "cancelled",
                "message": "规划已取消"
            }
            
        except Exception as e:
            logger.error(f"取消规划失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    def get_graph_visualization(self, include_interaction_hooks: bool = False) -> str:
        """
        获取工作流图可视化

        Args:
            include_interaction_hooks: 是否包含交互模式钩子（预留功能）

        Returns:
            Mermaid格式的图形定义
        """
        return self.graph.get_graph_visualization(include_interaction_hooks=include_interaction_hooks)

    def get_automatic_mode_info(self) -> Dict[str, Any]:
        """
        获取全自动模式信息

        Returns:
            全自动模式的详细信息
        """
        return {
            "mode": "automatic",
            "description": "完全自动化的旅行规划模式",
            "features": [
                "无需用户交互",
                "完整的规划流程",
                "双模运行支持（精准续航规划 vs 通用驾驶辅助）",
                "实时进度更新",
                "状态管理和恢复",
                "SSE事件流支持"
            ],
            "workflow_stages": [
                "核心意图分析",
                "多城市策略分析（如适用）",
                "驾驶情境分析（如适用）",
                "偏好分析",
                "行程生成",
                "行程优化"
            ],
            "interaction_hooks_enabled": self.enable_interaction_hooks,
            "interaction_hooks_available": [
                "用户确认节点（预留）",
                "用户反馈节点（预留）",
                "交互式规划方法（预留）",
                "交互式流式规划方法（预留）"
            ]
        }
    
    async def _save_planning_memory(
        self,
        user_id: str,
        query: str,
        final_state: Dict[str, Any]
    ):
        """保存规划记忆"""
        try:
            memory_content = {
                "original_query": query,
                "destinations": final_state.get("core_intent", {}).get("destinations", []),
                "days": final_state.get("core_intent", {}).get("days"),
                "planning_mode": final_state.get("planning_mode"),
                "success": not final_state.get("has_error", False),
                "session_id": final_state.get("session_id")
            }
            
            metadata = {
                "processing_time": final_state.get("processing_time_seconds", 0),
                "tokens_used": final_state.get("tokens_used", 0),
                "cost_estimate": final_state.get("cost_estimate", 0)
            }
            
            await self.memory_service.save_memory(
                user_id=user_id,
                memory_type="travel_planning",
                content=memory_content,
                metadata=metadata,
                importance=8 if not final_state.get("has_error") else 3,
                tags=["travel_planning", "langgraph"]
            )
            
            logger.info(f"规划记忆保存成功 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"保存规划记忆失败 - 用户: {user_id}, 错误: {str(e)}")
    
    def _calculate_progress(self, current_stage: Optional[str]) -> int:
        """根据当前阶段计算进度百分比"""
        stage_progress = {
            "intent_analysis": 20,
            "multi_city_strategy": 40,
            "driving_context": 50,
            "preference_analysis": 70,
            "itinerary_generation": 90,
            "optimization": 95,
            "completed": 100,
            "error": 0
        }
        
        return stage_progress.get(current_stage, 0)
