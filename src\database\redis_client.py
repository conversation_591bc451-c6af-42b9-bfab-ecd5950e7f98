"""
Redis客户端

实现AutoPilot AI系统中的L0/L1记忆层，提供任务状态管理、实时数据交换和进度跟踪功能。
"""
import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from contextlib import asynccontextmanager

import redis.asyncio as redis
from pydantic import BaseModel

from src.core.logger import get_logger
from src.core.config import get_settings

logger = get_logger("redis_client")


class TaskState(BaseModel):
    """任务状态模型"""
    task_id: str
    user_id: str
    status: str = "pending"  # pending, running, completed, failed
    original_query: str
    current_step: str = "init"
    progress_percentage: float = 0.0
    created_at: datetime
    updated_at: datetime
    state_json: Optional[str] = None  # AgentState的JSON序列化


class TaskStep(BaseModel):
    """任务步骤模型"""
    step_id: str
    parent_step_id: Optional[str] = None
    agent_name: str
    tool_name: Optional[str] = None
    tool_input: Optional[str] = None
    status: str = "pending"  # pending, running, completed, failed
    input: Optional[str] = None
    output: Optional[str] = None
    timestamp: datetime
    is_human_intervention: bool = False
    business_step_log: Optional[str] = None
    raw_model_trace: Optional[str] = None


class RedisClient:
    """Redis客户端 - 实现L0/L1记忆层"""
    
    def __init__(self, config=None):
        """初始化Redis客户端"""
        self.config = config or get_settings().redis
        self.logger = logger
        self._redis: Optional[redis.Redis] = None
        self._is_connected = False
        
    async def connect(self):
        """连接Redis"""
        if self._is_connected:
            return
            
        try:
            self._redis = redis.Redis(
                host=self.config.host,
                port=self.config.port,
                password=self.config.password,
                db=self.config.db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # 测试连接
            await self._redis.ping()
            self._is_connected = True
            self.logger.info(f"Redis连接成功: {self.config.host}:{self.config.port}")
            
        except Exception as e:
            self.logger.error(f"Redis连接失败: {str(e)}")
            raise
            
    async def disconnect(self):
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
            self._redis = None
            self._is_connected = False
            self.logger.info("Redis连接已断开")
            
    async def _ensure_connected(self):
        """确保Redis连接"""
        if not self._is_connected:
            await self.connect()
            
    # ==================== 任务状态管理 ====================
    
    async def create_task_state(self, task_id: str, user_id: str, original_query: str) -> TaskState:
        """创建任务状态"""
        await self._ensure_connected()
        
        now = datetime.now()
        task_state = TaskState(
            task_id=task_id,
            user_id=user_id,
            original_query=original_query,
            created_at=now,
            updated_at=now
        )
        
        # 存储到Redis - 将datetime对象转换为ISO格式字符串，处理None值
        key = f"task:{task_id}"
        state_dict = task_state.model_dump()
        
        # 处理所有字段，确保可序列化
        serializable_dict = {}
        for key_name, value in state_dict.items():
            if value is None:
                serializable_dict[key_name] = ""
            elif hasattr(value, 'isoformat'):
                # datetime对象
                serializable_dict[key_name] = value.isoformat()
            elif isinstance(value, (str, int, float, bool)):
                serializable_dict[key_name] = value
            else:
                # 其他对象转换为字符串
                serializable_dict[key_name] = str(value)
        
        await self._redis.hset(key, mapping=serializable_dict)
        await self._redis.expire(key, self.config.expiry_time)
        
        self.logger.info(f"任务状态已创建: {task_id}")
        return task_state
        
    async def get_task_state(self, task_id: str) -> Optional[TaskState]:
        """获取任务状态"""
        await self._ensure_connected()
        
        key = f"task:{task_id}"
        data = await self._redis.hgetall(key)
        
        if not data:
            return None
            
        # 处理反序列化
        deserialized_data = {}
        for key_name, value in data.items():
            if value == "":
                # 空字符串转回None
                deserialized_data[key_name] = None
            elif key_name in ['created_at', 'updated_at'] and isinstance(value, str):
                # 时间字段
                try:
                    deserialized_data[key_name] = datetime.fromisoformat(value)
                except ValueError:
                    deserialized_data[key_name] = datetime.now()
            elif key_name == 'progress_percentage' and isinstance(value, str):
                # 数值字段
                try:
                    deserialized_data[key_name] = float(value)
                except ValueError:
                    deserialized_data[key_name] = 0.0
            else:
                deserialized_data[key_name] = value
            
        return TaskState(**deserialized_data)
        
    async def update_task_state(self, task_id: str, **updates) -> bool:
        """更新任务状态"""
        await self._ensure_connected()
        
        key = f"task:{task_id}"
        
        # 处理所有更新字段，确保可序列化
        serializable_updates = {}
        for key_name, value in updates.items():
            if value is None:
                serializable_updates[key_name] = ""
            elif hasattr(value, 'isoformat'):
                # datetime对象
                serializable_updates[key_name] = value.isoformat()
            elif isinstance(value, (str, int, float, bool)):
                serializable_updates[key_name] = value
            else:
                # 其他对象转换为字符串
                serializable_updates[key_name] = str(value)
        
        # 更新时间戳
        serializable_updates['updated_at'] = datetime.now().isoformat()
        
        # 更新Redis
        result = await self._redis.hset(key, mapping=serializable_updates)
        await self._redis.expire(key, self.config.expiry_time)
        
        self.logger.debug(f"任务状态已更新: {task_id}, 字段: {list(serializable_updates.keys())}")
        return result > 0
        
    async def update_task_progress(self, task_id: str, progress: float, current_step: str = None):
        """更新任务进度"""
        updates = {'progress_percentage': progress}
        if current_step:
            updates['current_step'] = current_step
            
        await self.update_task_state(task_id, **updates)
        
    # ==================== 任务步骤记录 ====================
    
    async def record_task_step(self, task_id: str, step: TaskStep) -> bool:
        """记录任务步骤"""
        await self._ensure_connected()
        
        key = f"task_steps:{task_id}"
        
        # 将TaskStep转换为字典并处理序列化
        step_dict = step.model_dump()
        
        # 处理所有字段，确保可序列化
        serializable_dict = {}
        for key_name, value in step_dict.items():
            if value is None:
                serializable_dict[key_name] = ""
            elif hasattr(value, 'isoformat'):
                # datetime对象
                serializable_dict[key_name] = value.isoformat()
            elif isinstance(value, (str, int, float, bool, list, dict)):
                serializable_dict[key_name] = value
            else:
                # 其他对象转换为字符串
                serializable_dict[key_name] = str(value)
        
        step_json = json.dumps(serializable_dict, ensure_ascii=False, default=str)
        
        # 添加到列表末尾
        await self._redis.rpush(key, step_json)
        await self._redis.expire(key, self.config.expiry_time)
        
        self.logger.debug(f"任务步骤已记录: {task_id} -> {step.step_id}")
        return True
        
    async def get_task_steps(self, task_id: str, start: int = 0, end: int = -1) -> List[TaskStep]:
        """获取任务步骤历史"""
        await self._ensure_connected()
        
        key = f"task_steps:{task_id}"
        steps_json = await self._redis.lrange(key, start, end)
        
        steps = []
        for step_json in steps_json:
            try:
                step_data = json.loads(step_json)
                
                # 处理反序列化
                deserialized_step = {}
                for key_name, value in step_data.items():
                    if value == "":
                        # 空字符串转回None
                        deserialized_step[key_name] = None
                    elif key_name == 'timestamp' and isinstance(value, str):
                        # 时间字段
                        try:
                            deserialized_step[key_name] = datetime.fromisoformat(value)
                        except ValueError:
                            deserialized_step[key_name] = datetime.now()
                    elif key_name == 'is_human_intervention' and isinstance(value, str):
                        # 布尔字段
                        deserialized_step[key_name] = value.lower() == 'true'
                    else:
                        deserialized_step[key_name] = value
                
                steps.append(TaskStep(**deserialized_step))
            except Exception as e:
                self.logger.warning(f"解析步骤数据失败: {e}")
                
        return steps
        
    # ==================== L1短期记忆管理 ====================
    
    async def store_l1_memory(self, task_id: str, memory_key: str, memory_data: Any) -> bool:
        """存储L1短期记忆"""
        await self._ensure_connected()
        
        key = f"l1_memory:{task_id}:{memory_key}"
        
        try:
            if isinstance(memory_data, (dict, list)):
                data = json.dumps(memory_data, ensure_ascii=False, default=str)
            else:
                data = str(memory_data)
                
            await self._redis.set(key, data, ex=self.config.expiry_time)
            
            self.logger.debug(f"L1记忆已存储: {task_id}:{memory_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"存储L1记忆失败: {task_id}:{memory_key}, 错误: {str(e)}")
            # 尝试使用更安全的序列化方式
            try:
                # 使用repr()作为最后的备选方案
                data = repr(memory_data)
                await self._redis.set(key, data, ex=self.config.expiry_time)
                self.logger.warning(f"使用备选方案存储L1记忆: {task_id}:{memory_key}")
                return True
            except Exception as e2:
                self.logger.error(f"备选方案也失败: {task_id}:{memory_key}, 错误: {str(e2)}")
                return False
        
    async def get_l1_memory(self, task_id: str, memory_key: str) -> Optional[Any]:
        """获取L1短期记忆"""
        await self._ensure_connected()
        
        key = f"l1_memory:{task_id}:{memory_key}"
        data = await self._redis.get(key)
        
        if data is None:
            return None
            
        try:
            # 尝试解析为JSON
            return json.loads(data)
        except json.JSONDecodeError:
            # 如果不是JSON，返回原始字符串
            return data
            
    async def delete_l1_memory(self, task_id: str, memory_key: str) -> bool:
        """删除L1短期记忆"""
        await self._ensure_connected()
        
        key = f"l1_memory:{task_id}:{memory_key}"
        result = await self._redis.delete(key)
        
        self.logger.debug(f"L1记忆已删除: {task_id}:{memory_key}")
        return result > 0
        
    # ==================== 实时状态更新 ====================
    
    async def update_business_step_log(self, task_id: str, step_id: str, status: str):
        """更新业务步骤日志，存储为JSON字符串在主任务Hash下"""
        await self._ensure_connected()
        key = f"task:{task_id}"
        # 获取当前日志
        current_log_json = await self._redis.hget(key, "business_steps_log")
        if current_log_json:
            try:
                log_dict = json.loads(current_log_json)
            except Exception:
                log_dict = {}
        else:
            log_dict = {}
        # 更新日志
        log_dict[step_id] = status
        # 存储回Redis
        await self._redis.hset(key, mapping={"business_steps_log": json.dumps(log_dict, ensure_ascii=False)})
        await self._redis.expire(key, self.config.expiry_time)

    async def update_raw_model_trace(self, task_id: str, trace_dict: dict):
        """更新模型原始思考链，存储为JSON字符串在主任务Hash下"""
        await self._ensure_connected()
        key = f"task:{task_id}"
        # trace_dict 应包含 input/output/reasoning 等
        await self._redis.hset(key, mapping={"raw_model_trace": json.dumps(trace_dict, ensure_ascii=False)})
        await self._redis.expire(key, self.config.expiry_time)
        
    # ==================== 任务清理 ====================
    
    async def cleanup_task_data(self, task_id: str) -> bool:
        """清理任务相关数据"""
        await self._ensure_connected()
        
        try:
            # 删除所有相关的key
            keys_to_delete = [
                f"agent_state:{task_id}",
                f"task_steps:{task_id}",
                f"business_steps_log:{task_id}",
                f"raw_model_trace:{task_id}"
            ]
            
            # 删除L1记忆（使用模式匹配）
            l1_memory_pattern = f"l1_memory:{task_id}:*"
            l1_memory_keys = await self._redis.keys(l1_memory_pattern)
            keys_to_delete.extend(l1_memory_keys)
            
            if keys_to_delete:
                await self._redis.delete(*keys_to_delete)
                
            self.logger.info(f"任务数据已清理: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"清理任务数据失败: {task_id}, 错误: {str(e)}")
            return False
            
    # ==================== 工具方法 ====================
    
    async def get_task_summary(self, task_id: str) -> Dict[str, Any]:
        """获取任务摘要信息"""
        task_state = await self.get_task_state(task_id)
        if not task_state:
            return {}
            
        steps = await self.get_task_steps(task_id)
        
        return {
            'task_id': task_id,
            'status': task_state.status,
            'progress': task_state.progress_percentage,
            'current_step': task_state.current_step,
            'total_steps': len(steps),
            'completed_steps': len([s for s in steps if s.status == 'completed']),
            'created_at': task_state.created_at.isoformat(),
            'updated_at': task_state.updated_at.isoformat()
        }
        
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self._ensure_connected()
            await self._redis.ping()
            return True
        except Exception as e:
            self.logger.error(f"Redis健康检查失败: {str(e)}")
            return False


# 全局Redis客户端实例
_global_redis_client: Optional[RedisClient] = None


async def get_redis_client() -> RedisClient:
    """获取全局Redis客户端实例"""
    global _global_redis_client
    if _global_redis_client is None:
        _global_redis_client = RedisClient()
        await _global_redis_client.connect()
    return _global_redis_client


@asynccontextmanager
async def redis_client():
    """Redis客户端上下文管理器"""
    client = RedisClient()
    try:
        await client.connect()
        yield client
    finally:
        await client.disconnect() 